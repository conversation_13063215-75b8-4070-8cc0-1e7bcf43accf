{% extends 'base.html' %}
{% load discount_tags %}
{% load review_tags %}

{% block title %}Venues - CozyWish{% endblock %}

{% block content %}
<div class="container-fluid py-5">
    <div class="container">
    <div class="row">
        <!-- Search and Filter Sidebar -->
        <div class="col-lg-3">
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Search</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'venues_app:venue_list' %}">
                        <div class="mb-3">
                            <label for="query" class="form-label">Keyword</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" class="form-control" id="query" name="query" value="{{ search_form.query.value|default:'' }}" placeholder="Search venues, services, or categories...">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="location" class="form-label">Location</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
                                <input type="text" class="form-control" id="id_location" name="location" value="{{ search_form.location.value|default:'' }}" placeholder="City, State, or County" list="locationList" autocomplete="off">
                            </div>
                            <datalist id="locationList"></datalist>
                        </div>
                        <button type="submit" class="btn btn-primary w-100" aria-label="Search venues">Search</button>
                    </form>
                </div>
            </div>

            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filter</h5>
                </div>
                <div class="card-body">
                    <form method="get" action="{% url 'venues_app:venue_list' %}">
                        <!-- Preserve search parameters -->
                        {% if search_form.query.value %}
                        <input type="hidden" name="query" value="{{ search_form.query.value }}">
                        {% endif %}
                        {% if search_form.location.value %}
                        <input type="hidden" name="location" value="{{ search_form.location.value }}">
                        {% endif %}
                        {% if search_form.category.value %}
                        <input type="hidden" name="category" value="{{ search_form.category.value }}">
                        {% endif %}

                        <div class="mb-3">
                            <label for="sort_by" class="form-label">Sort By</label>
                            <select class="form-select" id="sort_by" name="sort_by">
                                <option value="">Sort by</option>
                                <option value="rating_high" {% if filter_form.sort_by.value == 'rating_high' %}selected{% endif %}>Rating: High to Low</option>
                                <option value="rating_low" {% if filter_form.sort_by.value == 'rating_low' %}selected{% endif %}>Rating: Low to High</option>
                                <option value="price_high" {% if filter_form.sort_by.value == 'price_high' %}selected{% endif %}>Price: High to Low</option>
                                <option value="price_low" {% if filter_form.sort_by.value == 'price_low' %}selected{% endif %}>Price: Low to High</option>
                                <option value="discount" {% if filter_form.sort_by.value == 'discount' %}selected{% endif %}>Discount: High to Low</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="venue_type" class="form-label">Venue Type</label>
                            <select class="form-select" id="venue_type" name="venue_type">
                                <option value="">All Types</option>
                                <option value="all" {% if filter_form.venue_type.value == 'all' %}selected{% endif %}>All Genders</option>
                                <option value="male" {% if filter_form.venue_type.value == 'male' %}selected{% endif %}>Male Only</option>
                                <option value="female" {% if filter_form.venue_type.value == 'female' %}selected{% endif %}>Female Only</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="id_state" class="form-label">State</label>
                            {{ filter_form.state }}
                        </div>

                        <div class="mb-3">
                            <label for="id_county" class="form-label">County</label>
                            {{ filter_form.county }}
                        </div>

                        <div class="mb-3">
                            <label for="id_city" class="form-label">City</label>
                            {{ filter_form.city }}
                        </div>

                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="has_discount" name="has_discount" {% if filter_form.has_discount.value %}checked{% endif %}>
                            <label class="form-check-label" for="has_discount">Has Discount</label>
                        </div>

                        <button type="submit" class="btn btn-primary w-100" aria-label="Apply filters">Apply Filters</button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Venues List -->
        <div class="col-lg-9">
            <h1 class="mb-4 fw-bold">Discover Venues</h1>

            {% if page_obj %}
            <div class="row g-4">
                {% for venue in page_obj %}
                <div class="col-lg-4 col-md-6">
                    <div class="card h-100 shadow-sm position-relative">
                        <!-- Discount Badge -->
                        {% venue_discount_summary venue %}

                        <!-- New on CozyWish Badge -->
                        {% venue_badge_card venue %}

                        <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}">
                            <img src="{{ venue.get_primary_image|default:'https://via.placeholder.com/317x177' }}" class="card-img-top" alt="{{ venue.name }}">
                        </a>
                        <div class="card-body">
                            <h5 class="card-title">
                                <a href="{% url 'venues_app:venue_detail' venue_slug=venue.slug %}" class="text-decoration-none text-dark">
                                    {{ venue.name }}
                                </a>
                            </h5>
                            <div class="mb-2">
                                <span class="fw-bold text-warning">{{ venue.get_average_rating }}★</span>
                                <span class="text-muted">({{ venue.get_review_count }})</span>
                            </div>
                            <p class="text-muted mb-2">{{ venue.city }}, {{ venue.state }}</p>

                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-light text-dark">{{ venue.category.category_name|default:"Spa & Wellness" }}</span>
                                {% if venue|has_venue_discounts or venue|has_platform_discounts %}
                                    <a href="{% url 'discount_app:venue_discounts' venue_id=venue.id %}" class="btn btn-sm btn-outline-success">
                                        <i class="fas fa-tags me-1"></i>View Deals
                                    </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="alert alert-info shadow-sm">
                <p class="mb-0">No venues found matching your criteria. Please try a different search or filter.</p>
            </div>
            {% endif %}
        </div>
    </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
  const stateSelect = document.getElementById('id_state');
  const countySelect = document.getElementById('id_county');
  const citySelect = document.getElementById('id_city');

  function fetchOptions(type, params, target) {
    const url = new URL('{% url "venues_app:get_location_data" %}', window.location.origin);
    url.searchParams.set('type', type);
    Object.entries(params).forEach(([k,v]) => url.searchParams.set(k, v));
    fetch(url)
      .then(r => r.json())
      .then(data => {
        target.innerHTML = '<option value="">Select '+type.slice(0,-1)+'</option>';
        data.options.forEach(opt => {
          const el = document.createElement('option');
          el.value = opt.value;
          el.textContent = opt.label;
          target.appendChild(el);
        });
      });
  }

  if(stateSelect){
    stateSelect.addEventListener('change', function(){
      countySelect.innerHTML = '<option value="">Select county</option>';
      citySelect.innerHTML = '<option value="">Select city</option>';
      if(this.value){
        fetchOptions('counties', {state:this.value}, countySelect);
      }
    });
  }

  if(countySelect){
    countySelect.addEventListener('change', function(){
      citySelect.innerHTML = '<option value="">Select city</option>';
      const state = stateSelect.value;
      if(state){
        fetchOptions('cities', {state:state, county:this.value}, citySelect);
      }
    });
  }
});
</script>
{% endblock %}
